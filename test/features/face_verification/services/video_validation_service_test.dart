import 'dart:io';
import 'package:flutter_test/flutter_test.dart';

import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';

void main() {
  group('VideoValidationService', () {
    late VideoValidationService videoValidationService;

    setUp(() {
      videoValidationService = VideoValidationService();
    });

    group('validateCoverageStats', () {
      test('should return success for good coverage stats', () {
        // Arrange
        const stats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 95,
          framesWithGoodCoverage: 85,
          averageCoverage: 82.5,
          minCoverage: 75.0,
          maxCoverage: 90.0,
        );

        // Act
        final result = videoValidationService.validateCoverageStats(stats);

        // Assert
        expect(result.isValid, isTrue);
        expect(result.successRate, equals(85.0));
        expect(result.message, contains('validation successful'));
        verify(mockLoggerService.info(
                '[FACE_VERIFICATION] Video validation successful: 85.0% good coverage'))
            .called(1);
      });

      test('should return failure for poor coverage stats', () {
        // Arrange
        const stats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 60,
          framesWithGoodCoverage: 40,
          averageCoverage: 65,
          minCoverage: 30.0,
          maxCoverage: 85.0,
        );

        // Act
        final result = videoValidationService.validateCoverageStats(stats);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.successRate, equals(40.0));
        expect(result.message, contains('insufficient face coverage'));
        verify(mockLoggerService.warning(
                '[FACE_VERIFICATION] Video validation failed: 40.0% good coverage (minimum: 70.0%)'))
            .called(1);
      });

      test('should return failure when no faces detected', () {
        // Arrange
        const stats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 0,
          framesWithGoodCoverage: 0,
          averageCoverage: 0,
          minCoverage: 0.0,
          maxCoverage: 0.0,
        );

        // Act
        final result = videoValidationService.validateCoverageStats(stats);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.successRate, equals(0.0));
        expect(result.message, contains('No face detected'));
        verify(mockLoggerService.warning(
                '[FACE_VERIFICATION] Video validation failed: 0.0% good coverage (minimum: 70.0%)'))
            .called(1);
      });

      test('should handle edge case at exactly 70% threshold', () {
        // Arrange
        const stats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 90,
          framesWithGoodCoverage: 70,
          averageCoverage: 78,
          minCoverage: 70.0,
          maxCoverage: 85.0,
        );

        // Act
        final result = videoValidationService.validateCoverageStats(stats);

        // Assert
        expect(result.isValid, isTrue);
        expect(result.successRate, equals(70.0));
        verify(mockLoggerService.info(
                '[FACE_VERIFICATION] Video validation successful: 70.0% good coverage'))
            .called(1);
      });

      test('should handle edge case just below 70% threshold', () {
        // Arrange
        const stats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 90,
          framesWithGoodCoverage: 69,
          averageCoverage: 77.5,
          minCoverage: 65.0,
          maxCoverage: 85.0,
        );

        // Act
        final result = videoValidationService.validateCoverageStats(stats);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.successRate, equals(69.0));
        verify(mockLoggerService.warning(
                '[FACE_VERIFICATION] Video validation failed: 69.0% good coverage (minimum: 70.0%)'))
            .called(1);
      });
    });

    group('calculateSuccessRate', () {
      test('should calculate correct success rate', () {
        // Arrange
        const stats = FaceCoverageStats(
          totalFrames: 200,
          framesWithFace: 180,
          framesWithGoodCoverage: 150,
          averageCoverage: 82,
          minCoverage: 70.0,
          maxCoverage: 95.0,
        );

        // Act
        final successRate = videoValidationService.calculateSuccessRate(stats);

        // Assert
        expect(successRate, equals(75.0)); // 150/200 * 100
      });

      test('should handle zero total frames', () {
        // Arrange
        const stats = FaceCoverageStats(
          totalFrames: 0,
          framesWithFace: 0,
          framesWithGoodCoverage: 0,
          averageCoverage: 0,
          minCoverage: 0.0,
          maxCoverage: 0.0,
        );

        // Act
        final successRate = videoValidationService.calculateSuccessRate(stats);

        // Assert
        expect(successRate, equals(0.0));
      });

      test('should handle perfect success rate', () {
        // Arrange
        const stats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 100,
          framesWithGoodCoverage: 100,
          averageCoverage: 90,
          minCoverage: 80.0,
          maxCoverage: 95.0,
        );

        // Act
        final successRate = videoValidationService.calculateSuccessRate(stats);

        // Assert
        expect(successRate, equals(100.0));
      });
    });

    group('generateValidationMessage', () {
      test('should generate success message for valid stats', () {
        // Arrange
        const stats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 95,
          framesWithGoodCoverage: 85,
          averageCoverage: 82.5,
          minCoverage: 75.0,
          maxCoverage: 90.0,
        );

        // Act
        final message =
            videoValidationService.generateValidationMessage(stats, true);

        // Assert
        expect(message, contains('validation successful'));
        expect(message, contains('85.0%'));
        expect(message, contains('average coverage: 82.5%'));
      });

      test('should generate failure message for invalid stats', () {
        // Arrange
        const stats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 60,
          framesWithGoodCoverage: 40,
          averageCoverage: 65,
          minCoverage: 30.0,
          maxCoverage: 85.0,
        );

        // Act
        final message =
            videoValidationService.generateValidationMessage(stats, false);

        // Assert
        expect(message, contains('insufficient face coverage'));
        expect(message, contains('40.0%'));
        expect(message, contains('minimum required: 70.0%'));
      });

      test('should generate no face detected message', () {
        // Arrange
        const stats = FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 0,
          framesWithGoodCoverage: 0,
          averageCoverage: 0,
          minCoverage: 0.0,
          maxCoverage: 0.0,
        );

        // Act
        final message =
            videoValidationService.generateValidationMessage(stats, false);

        // Assert
        expect(message, contains('No face detected'));
        expect(message, contains('Please ensure'));
      });
    });
  });
}
